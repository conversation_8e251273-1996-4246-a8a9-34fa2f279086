<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coriolis采样器颗粒物采集3D演示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 100;
            min-width: 250px;
        }
        
        #particle-counter {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 100;
            min-width: 200px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 8px;
            z-index: 100;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .stop-btn {
            background: #f44336 !important;
        }
        
        .stop-btn:hover {
            background: #da190b !important;
        }
        
        .clear-btn {
            background: #ff9800 !important;
        }
        
        .clear-btn:hover {
            background: #e68900 !important;
        }
        
        .view-btn {
            background: #2196F3 !important;
        }
        
        .view-btn:hover {
            background: #1976D2 !important;
        }
        
        #flow-control {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
        }
        
        input[type="range"] {
            width: 120px;
        }
        
        .particle-size {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .particle-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .efficiency-bar {
            width: 60px;
            height: 8px;
            background: #333;
            border-radius: 4px;
            margin-left: 10px;
            overflow: hidden;
        }
        
        .efficiency-fill {
            height: 100%;
            background: linear-gradient(to right, #ff4444, #ffaa44, #44ff44);
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="info-panel">
            <h3>采样器状态</h3>
            <div>流量: <span id="flow-rate">200</span> L/min</div>
            <div>采样时间: <span id="sampling-time">0</span> s</div>
            <div>总采样体积: <span id="total-volume">0</span> L</div>
            <div>状态: <span id="status">待机</span></div>
        </div>
        
        <div id="particle-counter">
            <h3>颗粒物计数</h3>
            <div class="particle-size">
                <div class="particle-color" style="background: white;"></div>
                <span>0.5μm: <span id="count-05">0</span></span>
                <div class="efficiency-bar"><div class="efficiency-fill" style="width: 10%"></div></div>
            </div>
            <div class="particle-size">
                <div class="particle-color" style="background: lightblue;"></div>
                <span>1μm: <span id="count-1">0</span></span>
                <div class="efficiency-bar"><div class="efficiency-fill" style="width: 25%"></div></div>
            </div>
            <div class="particle-size">
                <div class="particle-color" style="background: blue;"></div>
                <span>2μm: <span id="count-2">0</span></span>
                <div class="efficiency-bar"><div class="efficiency-fill" style="width: 50%"></div></div>
            </div>
            <div class="particle-size">
                <div class="particle-color" style="background: green;"></div>
                <span>3μm: <span id="count-3">0</span></span>
                <div class="efficiency-bar"><div class="efficiency-fill" style="width: 70%"></div></div>
            </div>
            <div class="particle-size">
                <div class="particle-color" style="background: yellow;"></div>
                <span>5μm: <span id="count-5">0</span></span>
                <div class="efficiency-bar"><div class="efficiency-fill" style="width: 85%"></div></div>
            </div>
            <div class="particle-size">
                <div class="particle-color" style="background: red;"></div>
                <span>10μm: <span id="count-10">0</span></span>
                <div class="efficiency-bar"><div class="efficiency-fill" style="width: 95%"></div></div>
            </div>
        </div>
        
        <div id="controls">
            <button id="start-btn">开始采样</button>
            <button id="stop-btn" class="stop-btn" disabled>停止采样</button>
            <button id="clear-btn" class="clear-btn">清空收集盘</button>
            
            <div id="flow-control">
                <label>流量:</label>
                <input type="range" id="flow-slider" min="100" max="300" value="200">
                <span id="flow-display">200 L/min</span>
            </div>
            
            <button class="view-btn" onclick="setView('front')">正视图</button>
            <button class="view-btn" onclick="setView('side')">侧视图</button>
            <button class="view-btn" onclick="setView('top')">俯视图</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <script>
        // 全局变量
        let scene, camera, renderer, controls;
        let sampler, particles = [], airflowLines = [];
        let isRunning = false;
        let startTime = 0;
        let flowRate = 200;
        let particleCounts = { '0.5': 0, '1': 0, '2': 0, '3': 0, '5': 0, '10': 0 };
        let collectedParticles = [];
        
        // 颗粒物配置
        const particleConfig = {
            '0.5': { color: 0xffffff, size: 0.5, weight: 0.1, efficiency: 0.1 },
            '1': { color: 0xadd8e6, size: 0.8, weight: 0.2, efficiency: 0.25 },
            '2': { color: 0x0000ff, size: 1.2, weight: 0.4, efficiency: 0.5 },
            '3': { color: 0x00ff00, size: 1.8, weight: 0.6, efficiency: 0.7 },
            '5': { color: 0xffff00, size: 2.5, weight: 0.8, efficiency: 0.85 },
            '10': { color: 0xff0000, size: 3.5, weight: 1.0, efficiency: 0.95 }
        };
        
        init();
        animate();
        
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x2a2a2a);
            
            // 创建摄像机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 2000);
            camera.position.set(100, 80, 100);
            camera.lookAt(0, 0, 0);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.physicallyCorrectLights = true;
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.minDistance = 50;
            controls.maxDistance = 300;
            
            // 创建光源
            setupLighting();
            
            // 创建场景元素
            createGround();
            createSampler();
            createAirflowVisualization();
            
            // 设置事件监听器
            setupEventListeners();
        }
        
        function setupLighting() {
            // 主光源
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(50, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 500;
            directionalLight.shadow.camera.left = -100;
            directionalLight.shadow.camera.right = 100;
            directionalLight.shadow.camera.top = 100;
            directionalLight.shadow.camera.bottom = -100;
            scene.add(directionalLight);
            
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);
            
            // 内部照明
            const innerLight = new THREE.PointLight(0xffffff, 0.5, 100);
            innerLight.position.set(0, 20, 0);
            scene.add(innerLight);
        }
        
        function createGround() {
            const groundGeometry = new THREE.PlaneGeometry(1200, 1200);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
        }

        function createSampler() {
            sampler = new THREE.Group();

            // 采样器主体 - 底座
            const baseGeometry = new THREE.CylinderGeometry(25, 25, 8, 32);
            const baseMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x888888,
                roughness: 0.3,
                metalness: 0.8
            });
            const base = new THREE.Mesh(baseGeometry, baseMaterial);
            base.position.y = 4;
            base.castShadow = true;
            sampler.add(base);

            // 透明采样腔体
            const chamberGeometry = new THREE.CylinderGeometry(20, 20, 40, 32);
            const chamberMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.85,
                roughness: 0.1,
                metalness: 0.1,
                transmission: 0.9,
                ior: 1.3,
                side: THREE.DoubleSide
            });
            const chamber = new THREE.Mesh(chamberGeometry, chamberMaterial);
            chamber.position.y = 28;
            chamber.castShadow = true;
            chamber.receiveShadow = true;
            sampler.add(chamber);

            // 顶盖
            const topGeometry = new THREE.CylinderGeometry(22, 22, 4, 32);
            const topMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x888888,
                roughness: 0.3,
                metalness: 0.8
            });
            const top = new THREE.Mesh(topGeometry, topMaterial);
            top.position.y = 50;
            top.castShadow = true;
            sampler.add(top);

            // 进气管道
            const pipeGeometry = new THREE.CylinderGeometry(3, 3, 30, 16);
            const pipeMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x666666,
                roughness: 0.4,
                metalness: 0.7
            });
            const pipe = new THREE.Mesh(pipeGeometry, pipeMaterial);
            pipe.position.set(15, 65, 0);
            pipe.rotation.z = Math.PI / 6;
            pipe.castShadow = true;
            sampler.add(pipe);

            // 弯曲进气口
            const inletGeometry = new THREE.CylinderGeometry(4, 3, 8, 16);
            const inlet = new THREE.Mesh(inletGeometry, pipeMaterial);
            inlet.position.set(8, 55, 0);
            inlet.rotation.z = Math.PI / 3;
            inlet.castShadow = true;
            sampler.add(inlet);

            // 收集盘
            const collectorGeometry = new THREE.CylinderGeometry(18, 18, 2, 32);
            const collectorMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xaaaaaa,
                roughness: 0.2,
                metalness: 0.6
            });
            const collector = new THREE.Mesh(collectorGeometry, collectorMaterial);
            collector.position.y = 9;
            collector.castShadow = true;
            collector.receiveShadow = true;
            sampler.add(collector);

            scene.add(sampler);
        }

        function createAirflowVisualization() {
            // 创建螺旋气流线条
            for (let i = 0; i < 8; i++) {
                const points = [];
                const radius = 15;
                const height = 40;
                const turns = 3;

                for (let j = 0; j <= 100; j++) {
                    const t = j / 100;
                    const angle = (i * Math.PI / 4) + (t * turns * Math.PI * 2);
                    const x = Math.cos(angle) * radius * (1 - t * 0.3);
                    const z = Math.sin(angle) * radius * (1 - t * 0.3);
                    const y = 48 - (t * height);
                    points.push(new THREE.Vector3(x, y, z));
                }

                const geometry = new THREE.BufferGeometry().setFromPoints(points);
                const material = new THREE.LineBasicMaterial({
                    color: 0x4444ff,
                    transparent: true,
                    opacity: 0.3
                });
                const line = new THREE.Line(geometry, material);
                airflowLines.push(line);
                scene.add(line);
            }
        }

        function createParticle(size) {
            const config = particleConfig[size];
            const geometry = new THREE.SphereGeometry(config.size, 8, 6);
            const material = new THREE.MeshPhysicalMaterial({
                color: config.color,
                transparent: true,
                opacity: 0.8,
                roughness: 0.4
            });

            const particle = new THREE.Mesh(geometry, material);
            particle.castShadow = true;

            // 设置初始位置（进气口）
            particle.position.set(8 + Math.random() * 4 - 2, 55, Math.random() * 4 - 2);

            // 粒子属性
            particle.userData = {
                size: size,
                weight: config.weight,
                velocity: new THREE.Vector3(
                    -0.5 + Math.random() * 0.2,
                    -0.1,
                    Math.random() * 0.2 - 0.1
                ),
                angularVelocity: 0,
                radius: 0,
                angle: Math.random() * Math.PI * 2,
                collected: false,
                inChamber: false
            };

            scene.add(particle);
            particles.push(particle);

            return particle;
        }

        function updateParticles() {
            for (let i = particles.length - 1; i >= 0; i--) {
                const particle = particles[i];
                const userData = particle.userData;

                // 检查是否进入采样腔体
                const distanceFromCenter = Math.sqrt(
                    particle.position.x * particle.position.x +
                    particle.position.z * particle.position.z
                );

                if (particle.position.y < 48 && particle.position.y > 8 && distanceFromCenter < 20) {
                    userData.inChamber = true;
                }

                if (userData.inChamber && !userData.collected) {
                    // Coriolis效应模拟
                    const centerDistance = Math.sqrt(
                        particle.position.x * particle.position.x +
                        particle.position.z * particle.position.z
                    );

                    // 根据颗粒大小调整旋转参数
                    const sizeWeight = userData.weight;
                    const rotationSpeed = (flowRate / 200) * (0.1 + sizeWeight * 0.05);
                    const centrifugalForce = sizeWeight * 0.02;

                    userData.angle += rotationSpeed;
                    userData.radius = Math.max(2, centerDistance + centrifugalForce - sizeWeight * 0.01);

                    // 螺旋运动
                    const targetX = Math.cos(userData.angle) * userData.radius;
                    const targetZ = Math.sin(userData.angle) * userData.radius;

                    particle.position.x += (targetX - particle.position.x) * 0.1;
                    particle.position.z += (targetZ - particle.position.z) * 0.1;

                    // 重力和下降速度
                    const fallSpeed = 0.2 + sizeWeight * 0.3;
                    userData.velocity.y = -fallSpeed * (flowRate / 200);
                } else {
                    // 直线运动（进气管道中）
                    const speed = (flowRate / 200) * 2;
                    userData.velocity.x *= 0.98;
                    userData.velocity.z *= 0.98;
                    userData.velocity.y -= 0.01; // 重力
                }

                // 更新位置
                particle.position.add(userData.velocity);

                // 碰撞检测
                if (userData.inChamber) {
                    const wallDistance = Math.sqrt(
                        particle.position.x * particle.position.x +
                        particle.position.z * particle.position.z
                    );

                    if (wallDistance > 19) {
                        // 壁面碰撞
                        const normal = new THREE.Vector3(particle.position.x, 0, particle.position.z).normalize();
                        userData.velocity.reflect(normal);
                        userData.velocity.multiplyScalar(0.7); // 能量损失
                    }
                }

                // 收集检测
                if (particle.position.y <= 10 && !userData.collected) {
                    userData.collected = true;

                    // 根据收集效率决定是否被收集
                    const efficiency = particleConfig[userData.size].efficiency;
                    if (Math.random() < efficiency) {
                        particleCounts[userData.size]++;
                        updateParticleCounter();

                        // 添加到收集盘
                        particle.position.y = 10;
                        const collectedRadius = Math.random() * 15;
                        const collectedAngle = Math.random() * Math.PI * 2;
                        particle.position.x = Math.cos(collectedAngle) * collectedRadius;
                        particle.position.z = Math.sin(collectedAngle) * collectedRadius;

                        collectedParticles.push(particle);
                    } else {
                        // 未被收集，移除粒子
                        scene.remove(particle);
                        particles.splice(i, 1);
                    }
                }

                // 移除超出范围的粒子
                if (particle.position.y < -10 ||
                    Math.abs(particle.position.x) > 100 ||
                    Math.abs(particle.position.z) > 100) {
                    scene.remove(particle);
                    particles.splice(i, 1);
                }
            }
        }

        function generateParticles() {
            if (!isRunning) return;

            // 根据流量调整生成频率
            const generationRate = (flowRate / 200) * 0.3;

            if (Math.random() < generationRate) {
                // 随机选择颗粒大小（按真实分布）
                const sizes = ['0.5', '1', '2', '3', '5', '10'];
                const weights = [0.4, 0.25, 0.15, 0.1, 0.07, 0.03]; // 小颗粒更常见

                let random = Math.random();
                let selectedSize = sizes[0];

                for (let i = 0; i < weights.length; i++) {
                    random -= weights[i];
                    if (random <= 0) {
                        selectedSize = sizes[i];
                        break;
                    }
                }

                createParticle(selectedSize);
            }
        }

        function updateParticleCounter() {
            document.getElementById('count-05').textContent = particleCounts['0.5'];
            document.getElementById('count-1').textContent = particleCounts['1'];
            document.getElementById('count-2').textContent = particleCounts['2'];
            document.getElementById('count-3').textContent = particleCounts['3'];
            document.getElementById('count-5').textContent = particleCounts['5'];
            document.getElementById('count-10').textContent = particleCounts['10'];
        }

        function updateInfoPanel() {
            if (isRunning) {
                const currentTime = (Date.now() - startTime) / 1000;
                document.getElementById('sampling-time').textContent = currentTime.toFixed(1);
                document.getElementById('total-volume').textContent = (currentTime * flowRate / 60).toFixed(1);
                document.getElementById('status').textContent = '采样中';
            } else {
                document.getElementById('status').textContent = '待机';
            }

            document.getElementById('flow-rate').textContent = flowRate;
        }

        function setupEventListeners() {
            // 开始采样按钮
            document.getElementById('start-btn').addEventListener('click', () => {
                isRunning = true;
                startTime = Date.now();
                document.getElementById('start-btn').disabled = true;
                document.getElementById('stop-btn').disabled = false;
            });

            // 停止采样按钮
            document.getElementById('stop-btn').addEventListener('click', () => {
                isRunning = false;
                document.getElementById('start-btn').disabled = false;
                document.getElementById('stop-btn').disabled = true;
            });

            // 清空收集盘按钮
            document.getElementById('clear-btn').addEventListener('click', () => {
                // 清除收集的颗粒物
                collectedParticles.forEach(particle => {
                    scene.remove(particle);
                    const index = particles.indexOf(particle);
                    if (index > -1) {
                        particles.splice(index, 1);
                    }
                });
                collectedParticles = [];

                // 重置计数器
                particleCounts = { '0.5': 0, '1': 0, '2': 0, '3': 0, '5': 0, '10': 0 };
                updateParticleCounter();
            });

            // 流量控制滑块
            const flowSlider = document.getElementById('flow-slider');
            const flowDisplay = document.getElementById('flow-display');

            flowSlider.addEventListener('input', (e) => {
                flowRate = parseInt(e.target.value);
                flowDisplay.textContent = flowRate + ' L/min';
            });

            // 窗口大小调整
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        function setView(viewType) {
            switch(viewType) {
                case 'front':
                    camera.position.set(0, 30, 100);
                    camera.lookAt(0, 25, 0);
                    break;
                case 'side':
                    camera.position.set(100, 30, 0);
                    camera.lookAt(0, 25, 0);
                    break;
                case 'top':
                    camera.position.set(0, 120, 0);
                    camera.lookAt(0, 0, 0);
                    break;
            }
            controls.update();
        }

        function animateAirflow() {
            // 旋转气流线条以显示流动效果
            airflowLines.forEach((line, index) => {
                line.rotation.y += 0.01 * (flowRate / 200);
            });
        }

        function animate() {
            requestAnimationFrame(animate);

            // 更新控制器
            controls.update();

            // 生成新颗粒物
            generateParticles();

            // 更新颗粒物物理
            updateParticles();

            // 动画气流
            animateAirflow();

            // 更新信息面板
            updateInfoPanel();

            // 渲染场景
            renderer.render(scene, camera);
        }
    </script>
</body>
</html>
