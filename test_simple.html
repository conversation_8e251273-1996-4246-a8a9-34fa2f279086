<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>状态: <span id="status">加载中...</span></div>
        <div>Three.js版本: <span id="version">未知</span></div>
    </div>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.150.1/examples/js/controls/OrbitControls.js"></script>
    
    <script>
        let scene, camera, renderer, controls;
        let cube;
        
        window.addEventListener('load', () => {
            console.log('页面加载完成');
            document.getElementById('status').textContent = '初始化中...';
            
            try {
                init();
                animate();
                document.getElementById('status').textContent = '运行中';
            } catch (error) {
                console.error('初始化错误:', error);
                document.getElementById('status').textContent = '错误: ' + error.message;
            }
        });
        
        function init() {
            // 检查Three.js
            if (typeof THREE === 'undefined') {
                throw new Error('THREE.js未加载');
            }
            
            document.getElementById('version').textContent = THREE.REVISION;
            console.log('Three.js版本:', THREE.REVISION);
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x2a2a2a);
            
            // 创建摄像机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(5, 5, 5);
            camera.lookAt(0, 0, 0);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);
            
            // 创建控制器
            if (typeof THREE.OrbitControls !== 'undefined') {
                controls = new THREE.OrbitControls(camera, renderer.domElement);
            } else if (typeof OrbitControls !== 'undefined') {
                controls = new OrbitControls(camera, renderer.domElement);
            } else {
                console.warn('OrbitControls未找到，使用固定视角');
            }
            
            if (controls) {
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
            }
            
            // 创建光源
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);
            
            // 创建地面
            const groundGeometry = new THREE.PlaneGeometry(20, 20);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
            
            // 创建一个测试立方体
            const cubeGeometry = new THREE.BoxGeometry(2, 2, 2);
            const cubeMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            cube = new THREE.Mesh(cubeGeometry, cubeMaterial);
            cube.position.y = 1;
            cube.castShadow = true;
            scene.add(cube);
            
            // 创建采样器简化版本
            createSimpleSampler();
            
            // 窗口大小调整
            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            });
            
            console.log('场景初始化完成');
        }
        
        function createSimpleSampler() {
            // 简单的圆柱形采样器
            const samplerGeometry = new THREE.CylinderGeometry(3, 3, 6, 16);
            const samplerMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x888888,
                transparent: true,
                opacity: 0.7
            });
            const sampler = new THREE.Mesh(samplerGeometry, samplerMaterial);
            sampler.position.set(-5, 3, 0);
            sampler.castShadow = true;
            scene.add(sampler);
            
            console.log('简单采样器创建完成');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            // 旋转立方体
            if (cube) {
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
            }
            
            // 更新控制器
            if (controls) {
                controls.update();
            }
            
            // 渲染场景
            renderer.render(scene, camera);
        }
    </script>
</body>
</html>
