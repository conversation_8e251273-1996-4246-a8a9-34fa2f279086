请使用 three.js 实现一个逼真的"Coriolis采样器颗粒物采集"3D演示。所有代码（包括HTML, CSS, JavaScript）都必须封装在一个独立的HTML文件中。

**场景设置：**
1. **平面：** 创建一个尺寸为1200*1200的深灰色、光滑的水平平面，它能接收阴影。
2. **Coriolis采样器：**
* **形状：** 在平面中心放置一个圆柱形的Coriolis采样器。采样器应具有清晰的轮廓：顶部有进气口，中间是透明的圆柱形采样腔体，底部有收集盘。整体高度约为圆柱直径的2倍。
* **材质：** 采样器主体为金属材质，设置 ```{ color: 0x888888, roughness: 0.3, metalness: 0.8 }```。采样腔体部分为透明材质，设置 ```{ color: 0xffffff, transparent: true, opacity: 0.85, roughness: 0.1, metalness: 0.1, transmission: 0.9, ior: 1.3, side: THREE.DoubleSide}```，应能看到内部颗粒物的运动。
* **进气管道：** 在采样器顶部连接一个进气管道，管道末端向下弯曲指向采样器入口。
* **收集盘：** 采样器底部有一个可拆卸的圆形收集盘，用于收集沉降的颗粒物。

**颗粒物采集模拟：**
1. **触发：** 页面在footer的位置提供一个"开始采样"按钮，点击后开始颗粒物采集过程。显示采样流量：200L/min。
2. **颗粒物生成与特性：**
* **颗粒物尺寸：** 生成6种不同尺寸的颗粒物：0.5μm、1μm、2μm、3μm、5μm、10μm。使用不同的颜色和大小来区分：
  - 0.5μm: 白色，极小点状
  - 1μm: 浅蓝色，小点状  
  - 2μm: 蓝色，小球状
  - 3μm: 绿色，中等球状
  - 5μm: 黄色，较大球状
  - 10μm: 红色，大球状
* **颗粒物材质：** 所有颗粒物使用半透明材质，设置 ```{ transparent: true, opacity: 0.8, roughness: 0.4 }```，并根据尺寸调整亮度。
3. **气流与旋转运动：**
* **进气流动：** 颗粒物从进气管道以200L/min的速度被吸入采样器，初始速度较快，呈直线运动。
* **Coriolis效应模拟：** 进入采样腔体后，颗粒物开始受到旋转气流影响，产生螺旋下降运动。旋转半径和速度根据颗粒物尺寸而变化：
  - 小颗粒物(0.5-2μm)：旋转半径大，下降速度慢，在腔体内停留时间长
  - 中等颗粒物(3-5μm)：旋转半径中等，下降速度适中
  - 大颗粒物(10μm)：旋转半径小，下降速度快，容易直接沉降到底部
4. **分离与沉降过程：**
* **尺寸分离：** 不同尺寸的颗粒物在旋转过程中逐渐分离，大颗粒物更容易向采样器中心聚集并快速下沉。
* **沉降模式：** 
  - 大颗粒物(5-10μm)：快速螺旋下降，直接落入收集盘
  - 中等颗粒物(2-3μm)：中等速度螺旋下降，部分贴壁运动
  - 小颗粒物(0.5-1μm)：缓慢螺旋运动，大部分随气流排出，少量沉降
* **收集效率：** 根据颗粒物尺寸显示不同的收集效率，大颗粒物收集效率高，小颗粒物收集效率低。

**物理效果模拟：**
1. **气流可视化：** 
* 在采样腔体内添加半透明的螺旋状气流线条，显示旋转气流的路径。
* 气流线条应该呈现从上到下的螺旋运动，旋转速度适中。
2. **颗粒物运动轨迹：**
* **重力影响：** 所有颗粒物受重力影响下降，但下降速度因尺寸而异。
* **离心力效应：** 在旋转过程中，颗粒物受到离心力作用，大颗粒物更容易被甩向外壁。
* **空气阻力：** 小颗粒物受空气阻力影响更大，运动轨迹更加复杂。
3. **碰撞与堆积：**
* **壁面碰撞：** 颗粒物与采样器内壁碰撞后会略微反弹或滑动。
* **颗粒物堆积：** 沉降到收集盘的颗粒物会逐渐堆积，形成不同颜色的分层效果。
* **堆积形态：** 大颗粒物主要堆积在收集盘中心，小颗粒物分布较为均匀。

**数据显示与监控：**
1. **实时数据面板：**
* 显示当前采样流量：200L/min
* 显示各尺寸颗粒物的计数和浓度
* 显示采样时间和总采样体积
* 显示各尺寸颗粒物的收集效率百分比
2. **颗粒物计数器：** 在屏幕角落显示实时的颗粒物计数，按尺寸分类统计。

**光照与渲染：**
1. **光源：**
* 设置一个主光源（模拟实验室照明），产生清晰的阴影。
* 添加环境光以提亮场景暗部，确保能清楚观察到小颗粒物。
* 在采样器内部添加柔和的内部照明，便于观察颗粒物运动。
2. **阴影：** 启用渲染器的阴影贴图。平面应能接收阴影，采样器和颗粒物应能投射阴影。
3. **透明度与反射：** 采样腔体的透明材质应能展示内部颗粒物运动，同时保持适当的反射效果。

**摄像机与控制：**
* 摄像机应从斜30度角俯视场景，确保能清晰观察到采样器内部的颗粒物分离过程。
* **必须使用正确的OrbitControls初始化方式：** `const controls = new OrbitControls(camera, renderer.domElement);` （注意：不是 `new THREE.OrbitControls`）
* 允许用户旋转视角以从不同角度观察采样过程，但限制缩放范围以保持最佳观察效果。

**交互功能：**
1. **控制按钮：**
* "开始采样" - 启动颗粒物生成和采集过程
* "停止采样" - 停止新颗粒物生成，但已有颗粒物继续运动直至沉降
* "清空收集盘" - 清除收集盘中的颗粒物，重置计数器
* "调整流量" - 滑块控制采样流量（100-300L/min），影响颗粒物运动速度
2. **视角切换：** 提供预设视角按钮（正视图、侧视图、俯视图），便于观察不同的采样细节。
